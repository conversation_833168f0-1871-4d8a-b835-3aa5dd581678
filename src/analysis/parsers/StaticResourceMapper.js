const fs = require('fs');
const path = require('path');

/**
 * Mapper for static resources in web applications
 */
class StaticResourceMapper {
  constructor() {
    // Common static file extensions
    this.staticExtensions = new Set([
      '.css', '.js', '.html', '.htm', '.jsp',
      '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',
      '.pdf', '.txt', '.xml', '.json',
      '.woff', '.woff2', '.ttf', '.eot',
      '.mp3', '.mp4', '.avi', '.mov',
      '.zip', '.tar', '.gz'
    ]);

    // Extensions to exclude from static resources
    this.excludedExtensions = new Set([
      '.java', '.class', '.jar', '.war'
    ]);

    // Common static directories
    this.staticDirectories = new Set([
      'css', 'js', 'javascript', 'scripts',
      'images', 'img', 'pics', 'pictures',
      'fonts', 'assets', 'static', 'resources',
      'public', 'web', 'www'
    ]);
  }

  /**
   * Scan webapp directory for static resources
   * @param {string} webappDir - Path to webapp directory
   * @returns {Promise<Route[]>} Array of static resource routes
   */
  async scanStaticResources(webappDir) {
    try {
      if (!fs.existsSync(webappDir)) {
        throw new Error(`Webapp directory not found: ${webappDir}`);
      }

      const routes = [];

      // Scan for all files in webapp directory
      const allFiles = this.getAllFiles(webappDir);

      for (const filePath of allFiles) {
        const route = this.createStaticRoute(filePath, webappDir);
        if (route) {
          routes.push(route);
        }
      }

      return routes;
    } catch (error) {
      throw new Error(`Failed to scan static resources: ${error.message}`);
    }
  }

  /**
   * Recursively get all files in a directory
   * @param {string} dir - Directory to scan
   * @returns {string[]} Array of file paths
   */
  getAllFiles(dir) {
    const files = [];

    const scanDir = (currentDir) => {
      const items = fs.readdirSync(currentDir);

      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          scanDir(fullPath);
        } else if (stat.isFile()) {
          files.push(fullPath);
        }
      }
    };

    scanDir(dir);
    return files;
  }

  /**
   * Create a route for a static file
   * @param {string} filePath - Absolute path to the file
   * @param {string} webappDir - Webapp root directory
   * @returns {Route|null} Route object or null if not a static resource
   */
  createStaticRoute(filePath, webappDir) {
    const relativePath = path.relative(webappDir, filePath);
    const ext = path.extname(filePath).toLowerCase();
    const dirname = path.dirname(relativePath);
    
    // Skip WEB-INF and META-INF directories (not publicly accessible)
    if (relativePath.startsWith('WEB-INF') || relativePath.startsWith('META-INF')) {
      return null;
    }

    // Determine if this is a static resource
    const isStatic = this.isStaticResource(filePath, relativePath);
    if (!isStatic) {
      return null;
    }

    // Create web path (normalize separators for web URLs)
    const webPath = '/' + relativePath.replace(/\\/g, '/');
    
    return {
      path: webPath,
      method: 'GET',
      type: this.getResourceType(ext, relativePath),
      source: 'filesystem',
      confidence: this.calculateConfidence(ext, dirname),
      metadata: {
        filePath: path.relative(process.cwd(), filePath),
        fileSize: this.getFileSize(filePath),
        extension: ext,
        directory: dirname,
        mimeType: this.getMimeType(ext)
      }
    };
  }

  /**
   * Determine if a file is a static resource
   * @param {string} filePath - File path
   * @param {string} relativePath - Relative path from webapp root
   * @returns {boolean} True if static resource
   */
  isStaticResource(filePath, relativePath) {
    const ext = path.extname(filePath).toLowerCase();
    const dirname = path.dirname(relativePath).toLowerCase();

    // Exclude certain file types
    if (this.excludedExtensions.has(ext)) {
      return false;
    }

    // Check by extension
    if (this.staticExtensions.has(ext)) {
      return true;
    }

    // Check by directory
    const dirParts = dirname.split(path.sep);
    for (const part of dirParts) {
      if (this.staticDirectories.has(part.toLowerCase())) {
        return true;
      }
    }

    // Files in root webapp directory are likely static
    if (dirname === '.' || dirname === '') {
      return true;
    }

    return false;
  }

  /**
   * Get resource type based on extension and path
   * @param {string} ext - File extension
   * @param {string} relativePath - Relative path
   * @returns {string} Resource type
   */
  getResourceType(ext, relativePath) {
    switch (ext) {
      case '.jsp':
      case '.html':
      case '.htm':
        return 'page';
      case '.css':
        return 'stylesheet';
      case '.js':
        return 'script';
      case '.png':
      case '.jpg':
      case '.jpeg':
      case '.gif':
      case '.svg':
      case '.ico':
        return 'image';
      case '.woff':
      case '.woff2':
      case '.ttf':
      case '.eot':
        return 'font';
      case '.pdf':
      case '.txt':
      case '.xml':
      case '.json':
        return 'document';
      default:
        return 'static';
    }
  }

  /**
   * Calculate confidence score for static resource
   * @param {string} ext - File extension
   * @param {string} dirname - Directory name
   * @returns {number} Confidence score (0-1)
   */
  calculateConfidence(ext, dirname) {
    let confidence = 0.7; // Base confidence for static resources

    // Higher confidence for known static extensions
    if (this.staticExtensions.has(ext)) {
      confidence += 0.2;
    }

    // Higher confidence for files in static directories
    const dirParts = dirname.toLowerCase().split(path.sep);
    for (const part of dirParts) {
      if (this.staticDirectories.has(part)) {
        confidence += 0.1;
        break;
      }
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Get file size safely
   * @param {string} filePath - File path
   * @returns {number} File size in bytes, or 0 if error
   */
  getFileSize(filePath) {
    try {
      const stats = fs.statSync(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Get MIME type for file extension
   * @param {string} ext - File extension
   * @returns {string} MIME type
   */
  getMimeType(ext) {
    const mimeTypes = {
      '.html': 'text/html',
      '.htm': 'text/html',
      '.jsp': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.txt': 'text/plain',
      '.pdf': 'application/pdf',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject'
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }

  /**
   * Group static resources by type
   * @param {Route[]} routes - Array of static routes
   * @returns {Object} Grouped routes by type
   */
  groupByType(routes) {
    const grouped = {};
    
    routes.forEach(route => {
      const type = route.type;
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(route);
    });

    return grouped;
  }
}

module.exports = StaticResourceMapper;

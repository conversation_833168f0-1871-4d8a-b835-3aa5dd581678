const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

/**
 * Scanner for Java source files to extract routing annotations
 */
class JavaAnnotationScanner {
  constructor() {
    // Regex patterns for different annotations
    this.patterns = {
      webServlet: /@WebServlet\s*\(\s*([^)]+)\s*\)/g,
      requestMapping: /@RequestMapping\s*\(\s*([^)]+)\s*\)/g,
      getMapping: /@GetMapping\s*\(\s*([^)]+)\s*\)/g,
      postMapping: /@PostMapping\s*\(\s*([^)]+)\s*\)/g,
      putMapping: /@PutMapping\s*\(\s*([^)]+)\s*\)/g,
      deleteMapping: /@DeleteMapping\s*\(\s*([^)]+)\s*\)/g,
      patchMapping: /@PatchMapping\s*\(\s*([^)]+)\s*\)/g
    };
  }

  /**
   * Scan Java source directory for routing annotations
   * @param {string} sourceDir - Root directory to scan
   * @returns {Promise<Route[]>} Array of discovered routes
   */
  async scanJavaFiles(sourceDir) {
    try {
      const javaFiles = await glob('**/*.java', { 
        cwd: sourceDir,
        absolute: true 
      });

      const routes = [];
      for (const filePath of javaFiles) {
        const fileRoutes = await this.scanFile(filePath);
        routes.push(...fileRoutes);
      }

      return routes;
    } catch (error) {
      throw new Error(`Failed to scan Java files: ${error.message}`);
    }
  }

  /**
   * Scan a single Java file for annotations
   * @param {string} filePath - Path to Java file
   * @returns {Promise<Route[]>} Array of routes found in file
   */
  async scanFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const routes = [];

      // Extract class name for metadata
      const className = this.extractClassName(content, filePath);

      // Scan for different annotation types
      routes.push(...this.scanWebServletAnnotations(content, className, filePath));
      routes.push(...this.scanSpringAnnotations(content, className, filePath));

      return routes;
    } catch (error) {
      console.warn(`Warning: Could not scan file ${filePath}: ${error.message}`);
      return [];
    }
  }

  /**
   * Scan for @WebServlet annotations
   * @param {string} content - File content
   * @param {string} className - Class name
   * @param {string} filePath - File path
   * @returns {Route[]} Array of routes
   */
  scanWebServletAnnotations(content, className, filePath) {
    const routes = [];
    let match;

    while ((match = this.patterns.webServlet.exec(content)) !== null) {
      const annotationContent = match[1];
      const urlPatterns = this.extractUrlPatterns(annotationContent);
      
      urlPatterns.forEach(pattern => {
        routes.push({
          path: this.normalizeUrlPattern(pattern),
          method: 'ALL', // @WebServlet doesn't specify HTTP method
          type: 'servlet',
          source: 'annotation',
          confidence: 0.95, // Very high confidence for explicit annotations
          metadata: {
            className,
            annotation: '@WebServlet',
            filePath: path.relative(process.cwd(), filePath),
            originalPattern: pattern
          }
        });
      });
    }

    return routes;
  }

  /**
   * Scan for Spring MVC annotations
   * @param {string} content - File content
   * @param {string} className - Class name
   * @param {string} filePath - File path
   * @returns {Route[]} Array of routes
   */
  scanSpringAnnotations(content, className, filePath) {
    const routes = [];
    const mappings = [
      { pattern: this.patterns.requestMapping, method: 'ALL', annotation: '@RequestMapping' },
      { pattern: this.patterns.getMapping, method: 'GET', annotation: '@GetMapping' },
      { pattern: this.patterns.postMapping, method: 'POST', annotation: '@PostMapping' },
      { pattern: this.patterns.putMapping, method: 'PUT', annotation: '@PutMapping' },
      { pattern: this.patterns.deleteMapping, method: 'DELETE', annotation: '@DeleteMapping' },
      { pattern: this.patterns.patchMapping, method: 'PATCH', annotation: '@PatchMapping' }
    ];

    mappings.forEach(({ pattern, method, annotation }) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const annotationContent = match[1];
        const paths = this.extractPaths(annotationContent);
        
        paths.forEach(pathValue => {
          routes.push({
            path: this.normalizeUrlPattern(pathValue),
            method,
            type: 'controller',
            source: 'annotation',
            confidence: 0.95,
            metadata: {
              className,
              annotation,
              filePath: path.relative(process.cwd(), filePath),
              originalPath: pathValue
            }
          });
        });
      }
    });

    return routes;
  }

  /**
   * Extract URL patterns from @WebServlet annotation content
   * @param {string} annotationContent - Content inside annotation parentheses
   * @returns {string[]} Array of URL patterns
   */
  extractUrlPatterns(annotationContent) {
    const patterns = [];
    
    // Look for urlPatterns attribute
    const urlPatternsMatch = annotationContent.match(/urlPatterns\s*=\s*(\{[^}]+\}|"[^"]*")/);
    if (urlPatternsMatch) {
      patterns.push(...this.extractStringArray(urlPatternsMatch[1]));
    }

    // Look for value attribute (shorthand)
    const valueMatch = annotationContent.match(/(?:^|,)\s*(?:value\s*=\s*)?(\{[^}]+\}|"[^"]*")(?=\s*(?:,|$))/);
    if (valueMatch && !urlPatternsMatch) {
      patterns.push(...this.extractStringArray(valueMatch[1]));
    }

    return patterns;
  }

  /**
   * Extract paths from Spring mapping annotations
   * @param {string} annotationContent - Content inside annotation parentheses
   * @returns {string[]} Array of paths
   */
  extractPaths(annotationContent) {
    const paths = [];
    
    // Look for path or value attribute
    const pathMatch = annotationContent.match(/(?:path|value)\s*=\s*(\{[^}]+\}|"[^"]*")/);
    if (pathMatch) {
      paths.push(...this.extractStringArray(pathMatch[1]));
    } else {
      // If no explicit path/value, look for direct string
      const directMatch = annotationContent.match(/^"([^"]*)"$/);
      if (directMatch) {
        paths.push(directMatch[1]);
      }
    }

    return paths;
  }

  /**
   * Extract string array from annotation value
   * @param {string} value - String or array value
   * @returns {string[]} Array of strings
   */
  extractStringArray(value) {
    if (value.startsWith('{') && value.endsWith('}')) {
      // Array format: {"path1", "path2"}
      const arrayContent = value.slice(1, -1);
      return arrayContent.split(',')
        .map(item => item.trim())
        .map(item => item.replace(/^"(.*)"$/, '$1'))
        .filter(item => item.length > 0);
    } else if (value.startsWith('"') && value.endsWith('"')) {
      // Single string: "path"
      return [value.slice(1, -1)];
    }
    return [];
  }

  /**
   * Extract class name from Java file content
   * @param {string} content - File content
   * @param {string} filePath - File path for fallback
   * @returns {string} Class name
   */
  extractClassName(content, filePath) {
    // Look for class declaration - must be preceded by whitespace or start of line
    const classMatch = content.match(/(?:^|\s)class\s+(\w+)/m);
    if (classMatch) {
      return classMatch[1];
    }

    // Fallback to filename
    return path.basename(filePath, '.java');
  }

  /**
   * Normalize URL pattern for consistent format
   * @param {string} pattern - URL pattern
   * @returns {string} Normalized pattern
   */
  normalizeUrlPattern(pattern) {
    if (!pattern) return '/';
    
    // Ensure pattern starts with /
    let normalized = pattern.startsWith('/') ? pattern : '/' + pattern;
    
    // Handle wildcard patterns
    if (normalized.endsWith('/*')) {
      normalized = normalized.substring(0, normalized.length - 2) + '/**';
    }
    
    return normalized;
  }
}

module.exports = JavaAnnotationScanner;

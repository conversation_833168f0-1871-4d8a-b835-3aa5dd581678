const RouteDiscovery = require('./analysis/RouteDiscovery');

// Export main classes for library usage
module.exports = {
  RouteDiscovery,
  WebXmlParser: require('./analysis/parsers/WebXmlParser'),
  JavaAnnotationScanner: require('./analysis/parsers/JavaAnnotationScanner'),
  StaticResourceMapper: require('./analysis/parsers/StaticResourceMapper'),
  RouteManifestGenerator: require('./analysis/RouteManifestGenerator')
};

// CLI functionality if run directly
if (require.main === module) {
  const path = require('path');
  
  async function main() {
    try {
      const projectPath = process.argv[2] || './fixtures/source';
      const outputPath = process.argv[3] || './route-manifest.json';
      
      console.log('JSP to React Route Discovery Tool');
      console.log('================================\n');
      
      const discovery = new RouteDiscovery({
        includeStaticResources: true,
        includeWebXml: true,
        includeAnnotations: true,
        minConfidence: 0.0,
        deduplicateRoutes: true
      });
      
      const manifest = await discovery.discoverRoutes(projectPath);
      
      // Export manifest
      await discovery.exportManifest(outputPath, projectPath);
      console.log(`\nRoute manifest exported to: ${outputPath}`);
      
      // Print summary
      console.log('\n' + discovery.generateSummary());
      
      // Print React Router analysis
      const analysis = discovery.analyzeForReactRouter();
      console.log('\nReact Router Analysis:');
      console.log('=====================');
      console.log(`Dynamic routes: ${analysis.dynamicRoutes.length}`);
      console.log(`Static routes: ${analysis.staticRoutes.length}`);
      console.log(`Nested routes: ${analysis.nestedRoutes.length}`);
      
      if (analysis.suggestions.length > 0) {
        console.log('\nSuggestions:');
        analysis.suggestions.forEach(suggestion => {
          console.log(`- ${suggestion}`);
        });
      }
      
    } catch (error) {
      console.error('Error:', error.message);
      process.exit(1);
    }
  }
  
  main();
}

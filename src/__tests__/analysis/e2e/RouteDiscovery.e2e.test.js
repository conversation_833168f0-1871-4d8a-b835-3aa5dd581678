const path = require('path');
const fs = require('fs');
const RouteDiscovery = require('../../../analysis/RouteDiscovery');

describe('RouteDiscovery E2E Tests', () => {
  let discovery;
  const fixturesPath = path.join(__dirname, '../../../../fixtures/source');

  beforeEach(() => {
    discovery = new RouteDiscovery({
      includeStaticResources: true,
      includeWebXml: true,
      includeAnnotations: true,
      minConfidence: 0.0,
      deduplicateRoutes: true
    });
  });

  describe('Real JSP Project Analysis', () => {
    test('should discover routes from fixtures/source project', async () => {
      const manifest = await discovery.discoverRoutes(fixturesPath);

      // Basic validation
      expect(manifest).toBeDefined();
      expect(manifest.routes).toBeInstanceOf(Array);
      expect(manifest.metadata).toBeDefined();
      expect(manifest.metadata.sourceProject).toBe(fixturesPath);
      expect(manifest.metadata.timestamp).toBeDefined();

      // Should find routes from different sources
      const sources = new Set(manifest.routes.map(r => r.source));
      console.log('Found sources:', Array.from(sources));
      console.log('Total routes found:', manifest.routes.length);

      // Log route details for debugging
      manifest.routes.forEach(route => {
        console.log(`Route: ${route.method} ${route.path} (${route.type}, ${route.source}, confidence: ${route.confidence})`);
      });

      expect(manifest.routes.length).toBeGreaterThan(0);
    });

    test('should find servlet routes from @WebServlet annotations', async () => {
      const manifest = await discovery.discoverRoutes(fixturesPath);
      
      const servletRoutes = manifest.routes.filter(r => 
        r.source === 'annotation' && r.type === 'servlet'
      );

      expect(servletRoutes.length).toBeGreaterThan(0);
      
      // Should find the BlogController servlet routes
      const blogControllerRoutes = servletRoutes.filter(r =>
        r.metadata && r.metadata.className === 'BlogController'
      );

      expect(blogControllerRoutes.length).toBeGreaterThan(0);

      // Should find at least one route with posts in the path
      const postsRoute = blogControllerRoutes.find(r => r.path.includes('posts'));
      if (postsRoute) {
        expect(postsRoute.confidence).toBeGreaterThan(0.9);
      }
    });

    test('should find static resources (JSP, CSS, etc.)', async () => {
      const manifest = await discovery.discoverRoutes(fixturesPath);
      
      const staticRoutes = manifest.routes.filter(r => r.source === 'filesystem');
      expect(staticRoutes.length).toBeGreaterThan(0);

      // Should find JSP pages
      const jspRoutes = staticRoutes.filter(r => r.type === 'page');
      expect(jspRoutes.length).toBeGreaterThan(0);

      // Should find CSS files
      const cssRoutes = staticRoutes.filter(r => r.type === 'stylesheet');
      expect(cssRoutes.length).toBeGreaterThan(0);

      // Log static resources found
      console.log('JSP pages found:', jspRoutes.map(r => r.path));
      console.log('CSS files found:', cssRoutes.map(r => r.path));
    });

    test('should generate comprehensive statistics', async () => {
      const manifest = await discovery.discoverRoutes(fixturesPath);
      const stats = manifest.metadata.stats;

      expect(stats.total).toBeGreaterThan(0);
      expect(stats.byType).toBeDefined();
      expect(stats.bySource).toBeDefined();
      expect(stats.byMethod).toBeDefined();
      expect(stats.byConfidence).toBeDefined();
      expect(stats.averageConfidence).toBeGreaterThan(0);

      // Log statistics
      console.log('Statistics:', JSON.stringify(stats, null, 2));
    });

    test('should provide React Router analysis', async () => {
      await discovery.discoverRoutes(fixturesPath);
      const analysis = discovery.analyzeForReactRouter();

      expect(analysis).toBeDefined();
      expect(analysis.dynamicRoutes).toBeInstanceOf(Array);
      expect(analysis.staticRoutes).toBeInstanceOf(Array);
      expect(analysis.nestedRoutes).toBeInstanceOf(Array);
      expect(analysis.suggestions).toBeInstanceOf(Array);

      console.log('React Router Analysis:', JSON.stringify(analysis, null, 2));
    });

    test('should export and import manifest correctly', async () => {
      const manifest = await discovery.discoverRoutes(fixturesPath);
      const outputPath = path.join(__dirname, 'temp-manifest.json');

      // Export manifest
      await discovery.exportManifest(outputPath, fixturesPath);
      expect(fs.existsSync(outputPath)).toBe(true);

      // Read and validate exported file
      const exportedContent = fs.readFileSync(outputPath, 'utf8');
      const exportedManifest = JSON.parse(exportedContent);

      expect(exportedManifest.routes).toHaveLength(manifest.routes.length);
      expect(exportedManifest.metadata.sourceProject).toBe(fixturesPath);

      // Clean up
      fs.unlinkSync(outputPath);
    });

    test('should generate human-readable summary', async () => {
      await discovery.discoverRoutes(fixturesPath);
      const summary = discovery.generateSummary();

      expect(typeof summary).toBe('string');
      expect(summary).toContain('Route Discovery Summary');
      expect(summary).toContain('Total Routes:');
      expect(summary).toContain('By Type:');
      expect(summary).toContain('By Source:');

      console.log('Summary:\n', summary);
    });
  });

  describe('Specific Route Validation', () => {
    test('should correctly identify blog-related routes', async () => {
      const manifest = await discovery.discoverRoutes(fixturesPath);
      
      // Look for blog-related routes
      const blogRoutes = manifest.routes.filter(r => 
        r.path.includes('post') || 
        r.metadata.servletName === 'BlogController' ||
        r.metadata.className === 'BlogController'
      );

      expect(blogRoutes.length).toBeGreaterThan(0);
      
      console.log('Blog-related routes:');
      blogRoutes.forEach(route => {
        console.log(`  ${route.method} ${route.path} (${route.source})`);
      });
    });

    test('should find JSP pages with correct paths', async () => {
      const manifest = await discovery.discoverRoutes(fixturesPath);
      
      const jspPages = manifest.routes.filter(r => 
        r.type === 'page' && r.path.endsWith('.jsp')
      );

      expect(jspPages.length).toBeGreaterThan(0);

      // Check for expected JSP files
      const expectedJspFiles = ['posts.jsp', 'create.jsp', 'edit.jsp', 'post.jsp', 'error.jsp'];
      const foundJspFiles = jspPages.map(r => path.basename(r.path));

      expectedJspFiles.forEach(expectedFile => {
        expect(foundJspFiles).toContain(expectedFile);
      });

      console.log('Found JSP pages:', foundJspFiles);
    });

    test('should handle confidence scoring correctly', async () => {
      const manifest = await discovery.discoverRoutes(fixturesPath);
      
      // Annotation-based routes should have high confidence
      const annotationRoutes = manifest.routes.filter(r => r.source === 'annotation');
      annotationRoutes.forEach(route => {
        expect(route.confidence).toBeGreaterThanOrEqual(0.9);
      });

      // Static resources should have reasonable confidence
      const staticRoutes = manifest.routes.filter(r => r.source === 'filesystem');
      staticRoutes.forEach(route => {
        expect(route.confidence).toBeGreaterThanOrEqual(0.7);
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle non-existent project gracefully', async () => {
      const nonExistentPath = '/non/existent/project';
      const manifest = await discovery.discoverRoutes(nonExistentPath);

      expect(manifest.routes).toHaveLength(0);
      expect(manifest.metadata.sourceProject).toBe(nonExistentPath);
    });

    test('should continue discovery even if some components fail', async () => {
      // Test with a discovery instance that has some components disabled
      const partialDiscovery = new RouteDiscovery({
        includeStaticResources: false,
        includeWebXml: true,
        includeAnnotations: true
      });

      const manifest = await partialDiscovery.discoverRoutes(fixturesPath);
      
      // Should still find some routes even with static resources disabled
      expect(manifest.routes.length).toBeGreaterThan(0);
      
      // Should not have filesystem routes
      const filesystemRoutes = manifest.routes.filter(r => r.source === 'filesystem');
      expect(filesystemRoutes).toHaveLength(0);
    });
  });
});

const fs = require('fs');
const path = require('path');
const StaticResourceMapper = require('../../../analysis/parsers/StaticResourceMapper');

describe('StaticResourceMapper', () => {
  let mapper;
  let tempDir;

  beforeEach(() => {
    mapper = new StaticResourceMapper();
    tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up temp files
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('scanStaticResources', () => {
    test('should scan static resources in webapp directory', async () => {
      // Create test files
      const cssDir = path.join(tempDir, 'css');
      const jsDir = path.join(tempDir, 'js');
      const webInfDir = path.join(tempDir, 'WEB-INF');
      
      fs.mkdirSync(cssDir, { recursive: true });
      fs.mkdirSync(jsDir, { recursive: true });
      fs.mkdirSync(webInfDir, { recursive: true });
      
      fs.writeFileSync(path.join(cssDir, 'styles.css'), '/* css content */');
      fs.writeFileSync(path.join(jsDir, 'app.js'), '// js content');
      fs.writeFileSync(path.join(tempDir, 'index.jsp'), '<html></html>');
      fs.writeFileSync(path.join(webInfDir, 'web.xml'), '<web-app></web-app>');

      const routes = await mapper.scanStaticResources(tempDir);

      expect(routes.length).toBeGreaterThanOrEqual(3); // Should exclude WEB-INF/web.xml
      
      const cssRoute = routes.find(r => r.path === '/css/styles.css');
      expect(cssRoute).toBeDefined();
      expect(cssRoute.type).toBe('stylesheet');
      expect(cssRoute.metadata.mimeType).toBe('text/css');
      
      const jsRoute = routes.find(r => r.path === '/js/app.js');
      expect(jsRoute).toBeDefined();
      expect(jsRoute.type).toBe('script');
      
      const jspRoute = routes.find(r => r.path === '/index.jsp');
      expect(jspRoute).toBeDefined();
      expect(jspRoute.type).toBe('page');
    });

    test('should handle non-existent directory', async () => {
      await expect(mapper.scanStaticResources('/non/existent/dir'))
        .rejects.toThrow('Webapp directory not found');
    });
  });

  describe('createStaticRoute', () => {
    test('should create route for static file', () => {
      const filePath = path.join(tempDir, 'css', 'styles.css');
      fs.mkdirSync(path.dirname(filePath), { recursive: true });
      fs.writeFileSync(filePath, '/* css */');

      const route = mapper.createStaticRoute(filePath, tempDir);

      expect(route).toEqual({
        path: '/css/styles.css',
        method: 'GET',
        type: 'stylesheet',
        source: 'filesystem',
        confidence: expect.any(Number),
        metadata: {
          filePath: expect.stringContaining('styles.css'),
          fileSize: expect.any(Number),
          extension: '.css',
          directory: 'css',
          mimeType: 'text/css'
        }
      });
    });

    test('should return null for WEB-INF files', () => {
      const filePath = path.join(tempDir, 'WEB-INF', 'web.xml');
      fs.mkdirSync(path.dirname(filePath), { recursive: true });
      fs.writeFileSync(filePath, '<web-app></web-app>');

      const route = mapper.createStaticRoute(filePath, tempDir);
      expect(route).toBeNull();
    });

    test('should return null for META-INF files', () => {
      const filePath = path.join(tempDir, 'META-INF', 'context.xml');
      fs.mkdirSync(path.dirname(filePath), { recursive: true });
      fs.writeFileSync(filePath, '<context></context>');

      const route = mapper.createStaticRoute(filePath, tempDir);
      expect(route).toBeNull();
    });
  });

  describe('isStaticResource', () => {
    test('should identify static resources by extension', () => {
      expect(mapper.isStaticResource('/path/to/file.css', 'file.css')).toBe(true);
      expect(mapper.isStaticResource('/path/to/file.js', 'file.js')).toBe(true);
      expect(mapper.isStaticResource('/path/to/file.png', 'file.png')).toBe(true);
      expect(mapper.isStaticResource('/path/to/file.java', 'file.java')).toBe(false);
    });

    test('should identify static resources by directory', () => {
      expect(mapper.isStaticResource('/path/to/file', 'css/file')).toBe(true);
      expect(mapper.isStaticResource('/path/to/file', 'js/file')).toBe(true);
      expect(mapper.isStaticResource('/path/to/file', 'images/file')).toBe(true);
      expect(mapper.isStaticResource('/path/to/file', 'src/file')).toBe(false);
    });

    test('should identify files in root as static', () => {
      expect(mapper.isStaticResource('/path/to/file', 'file')).toBe(true);
      expect(mapper.isStaticResource('/path/to/file', './file')).toBe(true);
    });
  });

  describe('getResourceType', () => {
    test('should return correct resource types', () => {
      expect(mapper.getResourceType('.css', 'styles.css')).toBe('stylesheet');
      expect(mapper.getResourceType('.js', 'app.js')).toBe('script');
      expect(mapper.getResourceType('.png', 'logo.png')).toBe('image');
      expect(mapper.getResourceType('.jsp', 'index.jsp')).toBe('page');
      expect(mapper.getResourceType('.pdf', 'doc.pdf')).toBe('document');
      expect(mapper.getResourceType('.woff', 'font.woff')).toBe('font');
      expect(mapper.getResourceType('.unknown', 'file.unknown')).toBe('static');
    });
  });

  describe('calculateConfidence', () => {
    test('should calculate confidence scores', () => {
      expect(mapper.calculateConfidence('.css', 'css')).toBeGreaterThan(0.8);
      expect(mapper.calculateConfidence('.js', 'js')).toBeGreaterThan(0.8);
      expect(mapper.calculateConfidence('.unknown', 'unknown')).toBe(0.7);
    });
  });

  describe('getMimeType', () => {
    test('should return correct MIME types', () => {
      expect(mapper.getMimeType('.css')).toBe('text/css');
      expect(mapper.getMimeType('.js')).toBe('application/javascript');
      expect(mapper.getMimeType('.png')).toBe('image/png');
      expect(mapper.getMimeType('.pdf')).toBe('application/pdf');
      expect(mapper.getMimeType('.unknown')).toBe('application/octet-stream');
    });
  });

  describe('groupByType', () => {
    test('should group routes by type', () => {
      const routes = [
        { type: 'stylesheet', path: '/css/style.css' },
        { type: 'script', path: '/js/app.js' },
        { type: 'stylesheet', path: '/css/theme.css' },
        { type: 'image', path: '/img/logo.png' }
      ];

      const grouped = mapper.groupByType(routes);

      expect(grouped.stylesheet).toHaveLength(2);
      expect(grouped.script).toHaveLength(1);
      expect(grouped.image).toHaveLength(1);
    });
  });
});

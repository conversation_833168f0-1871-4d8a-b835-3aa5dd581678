/**
 * Route types and interfaces for the JSP to React migration tool
 */

/**
 * @typedef {Object} Route
 * @property {string} path - The route path
 * @property {string} method - HTTP method (GET, POST, etc.)
 * @property {string} type - Route type (servlet, jsp, static)
 * @property {string} source - Source of the route (web.xml, annotation, file)
 * @property {number} confidence - Confidence score (0-1)
 * @property {Object} metadata - Additional metadata
 */

/**
 * @typedef {Object} ServletMapping
 * @property {string} servletName - Name of the servlet
 * @property {string[]} urlPatterns - URL patterns mapped to this servlet
 * @property {string} servletClass - Fully qualified class name
 */

/**
 * @typedef {Object} RouteManifest
 * @property {Route[]} routes - List of discovered routes
 * @property {Object} metadata - Metadata about the discovery process
 * @property {string} metadata.timestamp - When the discovery was performed
 * @property {string} metadata.sourceProject - Source project path
 * @property {Object} metadata.stats - Statistics about discovered routes
 */

module.exports = {
  // Export types for JSDoc reference
};
